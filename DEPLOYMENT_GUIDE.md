# AI RAG System Deployment Guide

## Prerequisites

Before deploying the AI RAG system, ensure you have:

1. **Supabase Project**: Active Supabase project with database access
2. **OpenAI API Key**: Valid OpenAI API key with sufficient credits
3. **Admin Access**: Admin privileges in your UrbanEdge application

## Step 1: Database Setup

### 1.1 Enable pgvector Extension

In your Supabase SQL Editor, run:

```sql
-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;
```

### 1.2 Apply AI RAG Migration

Copy and execute the contents of `supabase/migrations/12_create_ai_rag_system.sql` in your Supabase SQL Editor. This will create:

- AI-specific tables (ai_documents, ai_embeddings, etc.)
- Vector search functions
- Row Level Security policies
- Embedding management functions

## Step 2: Environment Configuration

### 2.1 Update Environment Variables

Add these variables to your `.env` file:

```env
# AI Services Configuration
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Feature Flags
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_PROPERTY_EMBEDDINGS=true

# Optional: Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
```

### 2.2 Verify Existing Configuration

Ensure these existing variables are properly set:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Step 3: Install Dependencies

The required dependencies should already be installed. If not, run:

```bash
npm install openai axios uuid react-markdown remark-gfm
```

## Step 4: Initial Property Processing

### 4.1 Access AI Management Dashboard

1. Start your development server: `npm run dev`
2. Log in as an admin user
3. Navigate to `/admin/ai`

### 4.2 Process Existing Properties

1. In the AI Management dashboard, click "Process All Properties"
2. Monitor the progress - this will create vector embeddings for all existing properties
3. Wait for completion (may take several minutes depending on property count)

### 4.3 Verify Setup

1. Check the statistics dashboard shows embedding coverage
2. Click "Test AI Response" to verify AI functionality
3. Test the chatbot widget on any page

## Step 5: Feature Testing

### 5.1 Test AI Chatbot

1. Click the floating chat button (bottom-right of any page)
2. Try queries like:
   - "Show me 3 bedroom houses in Lagos"
   - "What properties do you have under ₦30 million?"
   - "I'm looking for investment opportunities"

### 5.2 Test AI Property Search

1. Navigate to `/ai-search`
2. Try natural language searches:
   - "Luxury apartments with swimming pool in Abuja"
   - "Family homes with garden near schools"
   - "Commercial properties for rent in Victoria Island"

### 5.3 Verify Admin Features

1. Check AI Management dashboard shows correct statistics
2. Verify new properties automatically get processed
3. Test embedding updates when property details change

## Step 6: Production Deployment

### 6.1 Environment Variables for Production

Ensure production environment has:

```env
VITE_OPENAI_API_KEY=your_production_openai_key
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_supabase_anon_key
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_PROPERTY_EMBEDDINGS=true
```

### 6.2 Database Migration for Production

1. Apply the same SQL migration to your production Supabase database
2. Process existing properties using the admin dashboard
3. Monitor performance and usage

### 6.3 Performance Monitoring

Set up monitoring for:
- OpenAI API usage and costs
- Database query performance
- Vector search response times
- User engagement with AI features

## Troubleshooting

### Common Issues

#### 1. "Failed to generate embeddings"
- **Cause**: Invalid or expired OpenAI API key
- **Solution**: Verify API key in environment variables
- **Check**: OpenAI dashboard for usage limits

#### 2. "No properties found" in AI search
- **Cause**: Properties not processed for embeddings
- **Solution**: Run "Process All Properties" in admin dashboard
- **Check**: Embedding coverage statistics

#### 3. Chatbot not responding
- **Cause**: Missing user authentication or API issues
- **Solution**: Ensure user is logged in, check API key
- **Check**: Browser console for error messages

#### 4. Vector search errors
- **Cause**: pgvector extension not enabled
- **Solution**: Run `CREATE EXTENSION IF NOT EXISTS vector;` in Supabase
- **Check**: Supabase logs for extension errors

### Debug Steps

1. **Check Environment Variables**
   ```javascript
   console.log('OpenAI Key:', import.meta.env.VITE_OPENAI_API_KEY ? 'Set' : 'Missing');
   ```

2. **Verify Database Tables**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_name LIKE 'ai_%';
   ```

3. **Check Embedding Coverage**
   ```sql
   SELECT 
     COUNT(*) as total_properties,
     COUNT(pe.id) as properties_with_embeddings
   FROM properties p
   LEFT JOIN property_embeddings pe ON p.id = pe.property_id;
   ```

4. **Test Vector Search**
   ```sql
   SELECT search_similar_properties(
     ARRAY[0.1, 0.2, 0.3]::vector(3), -- Test vector
     0.5, -- Threshold
     5    -- Limit
   );
   ```

## Performance Optimization

### 1. Embedding Processing
- Process properties in batches of 3-5 to avoid rate limits
- Schedule processing during off-peak hours
- Monitor OpenAI API usage and costs

### 2. Vector Search
- Use appropriate similarity thresholds (0.7-0.8 for good matches)
- Limit search results to reasonable numbers (5-20)
- Cache frequent search queries

### 3. Database Performance
- Monitor vector index performance
- Consider partitioning for large datasets
- Regular VACUUM and ANALYZE operations

## Security Considerations

### 1. API Key Security
- Never expose OpenAI API key in client-side code
- Use environment variables for all sensitive data
- Rotate API keys regularly

### 2. Data Privacy
- User conversations are stored securely
- Implement data retention policies
- Ensure GDPR compliance for user data

### 3. Access Control
- AI features require user authentication
- Admin-only access to AI management tools
- Proper RLS policies for all AI tables

## Monitoring & Maintenance

### Daily Tasks
- Monitor OpenAI API usage and costs
- Check AI system error logs
- Verify chatbot response quality

### Weekly Tasks
- Review embedding coverage for new properties
- Analyze user search patterns
- Update AI prompts based on feedback

### Monthly Tasks
- Performance optimization review
- Cost analysis and optimization
- Feature usage analytics review

## Support

For issues with the AI RAG implementation:

1. Check this deployment guide first
2. Review error logs in browser console
3. Check Supabase logs for database issues
4. Monitor OpenAI dashboard for API issues
5. Contact development team with specific error messages

---

This deployment guide ensures a smooth setup and operation of the AI RAG system in your UrbanEdge real estate application.

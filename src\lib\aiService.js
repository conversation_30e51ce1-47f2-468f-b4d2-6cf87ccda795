import OpenAI from 'openai';
import { supabase } from './supabase';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
});

/**
 * AI Service for real estate chatbot with RAG capabilities
 */
export const aiService = {
  /**
   * Generate embeddings for text content
   * @param {string} text - Text to embed
   * @returns {Promise<number[]>} Embedding vector
   */
  async generateEmbedding(text) {
    try {
      const response = await openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
        encoding_format: "float",
      });
      
      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw error;
    }
  },

  /**
   * Search for similar properties using vector similarity
   * @param {string} query - User query
   * @param {number} matchThreshold - Similarity threshold (0-1)
   * @param {number} matchCount - Number of results to return
   * @returns {Promise<Array>} Similar properties
   */
  async searchSimilarProperties(query, matchThreshold = 0.8, matchCount = 10) {
    try {
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);
      
      // Search using the database function
      const { data, error } = await supabase.rpc('search_similar_properties', {
        query_embedding: queryEmbedding,
        match_threshold: matchThreshold,
        match_count: matchCount
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching similar properties:', error);
      throw error;
    }
  },

  /**
   * Search for relevant documents for RAG context
   * @param {string} query - User query
   * @param {number} matchThreshold - Similarity threshold
   * @param {number} matchCount - Number of documents to return
   * @param {string[]} documentTypes - Filter by document types
   * @returns {Promise<Array>} Relevant documents
   */
  async searchRelevantDocuments(query, matchThreshold = 0.7, matchCount = 5, documentTypes = null) {
    try {
      const queryEmbedding = await this.generateEmbedding(query);
      
      const { data, error } = await supabase.rpc('search_relevant_documents', {
        query_embedding: queryEmbedding,
        match_threshold: matchThreshold,
        match_count: matchCount,
        document_types: documentTypes
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching relevant documents:', error);
      throw error;
    }
  },

  /**
   * Generate AI response with RAG context
   * @param {string} userMessage - User's message
   * @param {string} sessionId - Chat session ID
   * @param {Object} context - Additional context (user preferences, property context, etc.)
   * @returns {Promise<string>} AI response
   */
  async generateResponse(userMessage, sessionId, context = {}) {
    try {
      // Get relevant documents and properties for context
      const [relevantDocs, similarProperties] = await Promise.all([
        this.searchRelevantDocuments(userMessage, 0.7, 3),
        this.searchSimilarProperties(userMessage, 0.75, 5)
      ]);

      // Get recent conversation history
      const { data: recentMessages } = await supabase
        .from('ai_chat_messages')
        .select('message_type, content')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false })
        .limit(10);

      // Build context for the AI
      const systemPrompt = this.buildSystemPrompt(context);
      const ragContext = this.buildRAGContext(relevantDocs, similarProperties);
      const conversationHistory = this.buildConversationHistory(recentMessages || []);

      // Generate response using OpenAI
      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "system", content: ragContext },
          ...conversationHistory,
          { role: "user", content: userMessage }
        ],
        temperature: 0.7,
        max_tokens: 1000,
      });

      const aiResponse = response.choices[0].message.content;

      // Store the conversation
      await this.storeConversation(sessionId, userMessage, aiResponse, {
        model: "gpt-4o-mini",
        tokens_used: response.usage.total_tokens,
        relevant_docs: relevantDocs.length,
        similar_properties: similarProperties.length
      });

      return aiResponse;
    } catch (error) {
      console.error('Error generating AI response:', error);
      throw error;
    }
  },

  /**
   * Build system prompt for real estate assistant
   * @param {Object} context - User and session context
   * @returns {string} System prompt
   */
  buildSystemPrompt(context = {}) {
    const { userPreferences = {}, sessionType = 'general' } = context;
    
    return `You are UrbanEdge AI Assistant, a knowledgeable and helpful real estate expert specializing in the Nigerian property market. 

Your role:
- Help users find their perfect property
- Provide market insights and investment advice
- Answer questions about properties, neighborhoods, and real estate processes
- Use Nigerian Naira (₦) for all price discussions
- Be conversational, professional, and empathetic

Key guidelines:
- Always prioritize user safety and legitimate real estate practices
- Provide accurate information based on the context provided
- If you don't have specific information, clearly state that and offer to help find it
- Use Nigerian real estate terminology and market knowledge
- Format prices in Nigerian Naira (₦) with proper comma separation
- Be helpful but not pushy about property recommendations

Current session type: ${sessionType}
${userPreferences.budget ? `User budget range: ₦${userPreferences.budget.min?.toLocaleString()} - ₦${userPreferences.budget.max?.toLocaleString()}` : ''}
${userPreferences.location ? `Preferred locations: ${userPreferences.location.join(', ')}` : ''}
${userPreferences.propertyType ? `Preferred property types: ${userPreferences.propertyType.join(', ')}` : ''}`;
  },

  /**
   * Build RAG context from relevant documents and properties
   * @param {Array} relevantDocs - Relevant documents
   * @param {Array} similarProperties - Similar properties
   * @returns {string} RAG context
   */
  buildRAGContext(relevantDocs, similarProperties) {
    let context = "RELEVANT INFORMATION:\n\n";

    if (relevantDocs.length > 0) {
      context += "Documents:\n";
      relevantDocs.forEach((doc, index) => {
        context += `${index + 1}. ${doc.document_title} (${doc.document_type}): ${doc.content_chunk}\n`;
      });
      context += "\n";
    }

    if (similarProperties.length > 0) {
      context += "Available Properties:\n";
      similarProperties.forEach((prop, index) => {
        context += `${index + 1}. ${prop.title} - ${prop.location} - ₦${prop.price.toLocaleString()} (${prop.property_type} for ${prop.sale_type})\n`;
      });
      context += "\n";
    }

    context += "Use this information to provide accurate, helpful responses. If the user asks about specific properties, reference them by name and provide details.";
    
    return context;
  },

  /**
   * Build conversation history for context
   * @param {Array} messages - Recent messages
   * @returns {Array} Formatted conversation history
   */
  buildConversationHistory(messages) {
    return messages
      .reverse() // Reverse to get chronological order
      .slice(0, 8) // Limit to last 8 messages
      .map(msg => ({
        role: msg.message_type === 'user' ? 'user' : 'assistant',
        content: msg.content
      }));
  },

  /**
   * Store conversation in database
   * @param {string} sessionId - Chat session ID
   * @param {string} userMessage - User's message
   * @param {string} aiResponse - AI's response
   * @param {Object} metadata - Additional metadata
   */
  async storeConversation(sessionId, userMessage, aiResponse, metadata = {}) {
    try {
      // Store user message
      await supabase.from('ai_chat_messages').insert({
        session_id: sessionId,
        message_type: 'user',
        content: userMessage,
        metadata: {}
      });

      // Store AI response
      await supabase.from('ai_chat_messages').insert({
        session_id: sessionId,
        message_type: 'assistant',
        content: aiResponse,
        metadata
      });

      // Update session timestamp
      await supabase
        .from('ai_chat_sessions')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', sessionId);

    } catch (error) {
      console.error('Error storing conversation:', error);
      throw error;
    }
  },

  /**
   * Create or get AI chat session
   * @param {string} userId - User ID
   * @param {string} sessionType - Type of session
   * @param {Object} context - Session context
   * @returns {Promise<string>} Session ID
   */
  async createOrGetSession(userId, sessionType = 'general', context = {}) {
    try {
      const { data, error } = await supabase.rpc('upsert_ai_chat_session', {
        p_user_id: userId,
        p_session_type: sessionType,
        p_context: context
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating/getting session:', error);
      throw error;
    }
  },

  /**
   * Get user's AI chat sessions
   * @param {string} userId - User ID
   * @returns {Promise<Array>} User's chat sessions
   */
  async getUserSessions(userId) {
    try {
      const { data, error } = await supabase
        .from('ai_chat_sessions')
        .select(`
          id,
          session_type,
          status,
          created_at,
          updated_at,
          ai_chat_messages(count)
        `)
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      throw error;
    }
  },

  /**
   * Get session messages
   * @param {string} sessionId - Session ID
   * @param {number} limit - Number of messages to retrieve
   * @returns {Promise<Array>} Session messages
   */
  async getSessionMessages(sessionId, limit = 50) {
    try {
      const { data, error } = await supabase
        .from('ai_chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting session messages:', error);
      throw error;
    }
  }
};

export default aiService;

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  HomeIcon,
  CurrencyDollarIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../lib/aiService";
import ReactMarkdown from 'react-markdown';

const ChatbotWidget = () => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your UrbanEdge AI Assistant. I can help you find properties, provide market insights, and answer questions about real estate in Nigeria. How can I assist you today?",
      isBot: true,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Initialize AI session when user opens chat
  useEffect(() => {
    if (isOpen && user && !sessionId) {
      initializeSession();
    }
  }, [isOpen, user]);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Initialize AI chat session
  const initializeSession = async () => {
    try {
      const newSessionId = await aiService.createOrGetSession(
        user.id,
        'general',
        { source: 'chatbot_widget' }
      );
      setSessionId(newSessionId);

      // Load existing messages if any
      const existingMessages = await aiService.getSessionMessages(newSessionId, 20);
      if (existingMessages.length > 0) {
        const formattedMessages = existingMessages.map((msg, index) => ({
          id: index + 1,
          text: msg.content,
          isBot: msg.message_type === 'assistant',
          timestamp: new Date(msg.created_at),
        }));
        setMessages([...messages, ...formattedMessages]);
      }
    } catch (error) {
      console.error('Error initializing session:', error);
      setError('Failed to initialize chat session');
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    setError(null);
  };

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (inputValue.trim() === "" || isTyping) return;

    const userMessageText = inputValue.trim();
    setInputValue("");
    setError(null);

    // Add user message immediately
    const userMessage = {
      id: Date.now(),
      text: userMessageText,
      isBot: false,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // If no session and user is logged in, create one
      let currentSessionId = sessionId;
      if (!currentSessionId && user) {
        currentSessionId = await aiService.createOrGetSession(user.id, 'general');
        setSessionId(currentSessionId);
      }

      // Generate AI response
      let aiResponse;
      if (currentSessionId && user) {
        aiResponse = await aiService.generateResponse(
          userMessageText,
          currentSessionId,
          { source: 'chatbot_widget' }
        );
      } else {
        // Fallback for non-logged-in users
        aiResponse = "I'd be happy to help you with your real estate needs! For personalized property recommendations and detailed assistance, please log in to your account. I can still answer general questions about our properties and services.";
      }

      // Add AI response
      const botMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        isBot: true,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, botMessage]);

    } catch (error) {
      console.error('Error getting AI response:', error);
      setError('Sorry, I encountered an error. Please try again.');

      // Add error message
      const errorMessage = {
        id: Date.now() + 1,
        text: "I apologize, but I'm having trouble processing your request right now. Please try again in a moment, or contact our support team for immediate assistance.",
        isBot: true,
        timestamp: new Date(),
        isError: true,
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Quick action handlers
  const handleQuickAction = (action) => {
    const quickActions = {
      'find_properties': "I'm looking for properties to buy/rent. Can you help me find something suitable?",
      'market_insights': "Can you provide market insights and trends for the Nigerian real estate market?",
      'investment_advice': "I'm interested in real estate investment opportunities. What would you recommend?",
      'contact_agent': "I'd like to speak with a real estate agent about a specific property."
    };

    if (quickActions[action]) {
      setInputValue(quickActions[action]);
    }
  };

  return (
    <>
      {/* Chat Button */}
      <motion.button
        className="fixed bottom-6 right-6 z-50 w-14 h-14 rounded-full bg-gradient-to-r from-taupe to-brown text-white shadow-lg flex items-center justify-center group"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleChat}
        aria-label={isOpen ? "Close chat" : "Open AI chat assistant"}
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6" />
        ) : (
          <div className="relative">
            <ChatBubbleLeftRightIcon className="h-6 w-6" />
            <SparklesIcon className="h-3 w-3 absolute -top-1 -right-1 text-yellow-300 animate-pulse" />
          </div>
        )}
      </motion.button>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-24 right-6 z-50 w-full max-w-sm bg-white dark:bg-brown-dark rounded-lg shadow-xl overflow-hidden"
          >
            {/* Chat Header */}
            <div className="bg-gradient-to-r from-taupe to-brown text-white p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center mr-3">
                  <SparklesIcon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-heading font-bold flex items-center">
                    UrbanEdge AI Assistant
                    <span className="ml-2 text-xs bg-white/20 px-2 py-1 rounded-full">AI</span>
                  </h3>
                  <p className="text-xs opacity-80">
                    {user ? 'Personalized assistance ready' : 'General assistance available'}
                  </p>
                </div>
              </div>
              <button
                onClick={toggleChat}
                className="text-white hover:text-beige-light transition-colors"
                aria-label="Close chat"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Error Banner */}
            {error && (
              <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-3 text-sm">
                {error}
              </div>
            )}

            {/* Quick Actions */}
            {messages.length <= 1 && (
              <div className="p-3 bg-beige-light/30 dark:bg-brown/30 border-b border-beige-medium dark:border-brown">
                <p className="text-xs text-brown-dark dark:text-beige-light mb-2 font-medium">Quick actions:</p>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleQuickAction('find_properties')}
                    className="flex items-center p-2 bg-white dark:bg-brown-dark rounded-lg text-xs hover:bg-beige-light dark:hover:bg-brown transition-colors"
                  >
                    <HomeIcon className="h-4 w-4 mr-1 text-taupe" />
                    Find Properties
                  </button>
                  <button
                    onClick={() => handleQuickAction('market_insights')}
                    className="flex items-center p-2 bg-white dark:bg-brown-dark rounded-lg text-xs hover:bg-beige-light dark:hover:bg-brown transition-colors"
                  >
                    <CurrencyDollarIcon className="h-4 w-4 mr-1 text-taupe" />
                    Market Insights
                  </button>
                  <button
                    onClick={() => handleQuickAction('investment_advice')}
                    className="flex items-center p-2 bg-white dark:bg-brown-dark rounded-lg text-xs hover:bg-beige-light dark:hover:bg-brown transition-colors"
                  >
                    <SparklesIcon className="h-4 w-4 mr-1 text-taupe" />
                    Investment Tips
                  </button>
                  <button
                    onClick={() => handleQuickAction('contact_agent')}
                    className="flex items-center p-2 bg-white dark:bg-brown-dark rounded-lg text-xs hover:bg-beige-light dark:hover:bg-brown transition-colors"
                  >
                    <MapPinIcon className="h-4 w-4 mr-1 text-taupe" />
                    Contact Agent
                  </button>
                </div>
              </div>
            )}

            {/* Messages */}
            <div className="p-4 h-80 overflow-y-auto bg-beige-light/50 dark:bg-brown/50">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-4 flex ${
                    message.isBot ? "justify-start" : "justify-end"
                  }`}
                >
                  <div
                    className={`max-w-[85%] rounded-lg p-3 ${
                      message.isBot
                        ? `${message.isError ? 'bg-red-50 border border-red-200 text-red-800' : 'bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light'} rounded-tl-none`
                        : "bg-gradient-to-r from-taupe to-brown text-white rounded-tr-none"
                    }`}
                  >
                    {message.isBot ? (
                      <div className="prose prose-sm max-w-none">
                        <ReactMarkdown className="text-sm leading-relaxed">
                          {message.text}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <p className="text-sm">{message.text}</p>
                    )}
                    {message.timestamp && (
                      <p className={`text-xs mt-1 opacity-60 ${
                        message.isBot ? 'text-gray-500' : 'text-white/70'
                      }`}>
                        {message.timestamp.toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    )}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div className="mb-4 flex justify-start">
                  <div className="bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light rounded-lg rounded-tl-none p-3">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div
                          className="w-2 h-2 bg-taupe rounded-full animate-bounce"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-taupe rounded-full animate-bounce"
                          style={{ animationDelay: "150ms" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-taupe rounded-full animate-bounce"
                          style={{ animationDelay: "300ms" }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <form
              onSubmit={handleSubmit}
              className="p-3 border-t border-beige-medium dark:border-brown"
            >
              <div className="flex items-center">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={handleInputChange}
                  placeholder={user ? "Ask me about properties, market trends, or investment advice..." : "Type your message..."}
                  disabled={isTyping}
                  className="flex-grow p-2 rounded-l-md border border-r-0 border-beige-medium dark:border-brown focus:outline-none focus:ring-1 focus:ring-taupe bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light disabled:opacity-50"
                />

                <button
                  type="submit"
                  disabled={isTyping || !inputValue.trim()}
                  className="bg-gradient-to-r from-taupe to-brown text-white p-2 rounded-r-md border border-taupe hover:from-brown hover:to-taupe transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Send message"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </button>
              </div>
              {!user && (
                <p className="text-xs text-gray-500 mt-2 text-center">
                  <span className="text-taupe font-medium">Tip:</span> Log in for personalized property recommendations
                </p>
              )}
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ChatbotWidget;

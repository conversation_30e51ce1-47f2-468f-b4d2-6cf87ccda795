import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  SparklesIcon,
  MapPinIcon,
  HomeIcon,
  CurrencyDollarIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';
import { aiService } from '../../../lib/aiService';
import PropertyCard from '../../UI/PropertyCard';

const AIPropertySearch = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [error, setError] = useState(null);

  // Predefined search suggestions
  const searchSuggestions = [
    "3 bedroom apartment in Lagos under ₦30 million",
    "Luxury houses with swimming pool in Abuja",
    "Commercial properties for rent in Victoria Island",
    "Land for sale in Lekki with good investment potential",
    "Family homes with garden and garage in Ikeja",
    "Modern condos with gym and security in Ikoyi",
    "Affordable housing for first-time buyers",
    "Properties near schools and hospitals",
  ];

  useEffect(() => {
    // Filter suggestions based on query
    if (query.length > 2) {
      const filtered = searchSuggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(query.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [query]);

  const handleSearch = async (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setError(null);
    setShowSuggestions(false);

    try {
      // Use AI service to find similar properties
      const similarProperties = await aiService.searchSimilarProperties(
        searchQuery,
        0.75, // similarity threshold
        12 // max results
      );

      setResults(similarProperties);

      // If no results found, try with lower threshold
      if (similarProperties.length === 0) {
        const fallbackResults = await aiService.searchSimilarProperties(
          searchQuery,
          0.6, // lower threshold
          12
        );
        setResults(fallbackResults);
      }

    } catch (error) {
      console.error('Error searching properties:', error);
      setError('Failed to search properties. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleSearch();
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <SparklesIcon className="h-8 w-8 text-taupe mr-3" />
          <h1 className="text-3xl font-bold text-brown-dark dark:text-beige-light">
            AI Property Search
          </h1>
        </div>
        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Use natural language to find your perfect property. Our AI understands your preferences 
          and finds properties that match your needs.
        </p>
      </div>

      {/* Search Form */}
      <div className="relative mb-8">
        <form onSubmit={handleSubmit} className="relative">
          <div className="relative">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Describe your ideal property... (e.g., '3 bedroom house in Lagos with garden under ₦50 million')"
              className="w-full p-4 pr-12 text-lg border border-gray-300 dark:border-brown rounded-xl focus:outline-none focus:ring-2 focus:ring-taupe bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light"
              disabled={isSearching}
            />
            <button
              type="submit"
              disabled={isSearching || !query.trim()}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-taupe text-white rounded-lg hover:bg-brown transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSearching ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <SparklesIcon className="h-6 w-6" />
                </motion.div>
              ) : (
                <MagnifyingGlassIcon className="h-6 w-6" />
              )}
            </button>
          </div>
        </form>

        {/* Search Suggestions */}
        <AnimatePresence>
          {showSuggestions && suggestions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 bg-white dark:bg-brown-dark border border-gray-200 dark:border-brown rounded-lg shadow-lg z-10 mt-2"
            >
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left p-3 hover:bg-beige-light dark:hover:bg-brown transition-colors border-b border-gray-100 dark:border-brown last:border-b-0 first:rounded-t-lg last:rounded-b-lg"
                >
                  <div className="flex items-center">
                    <MagnifyingGlassIcon className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-brown-dark dark:text-beige-light">{suggestion}</span>
                  </div>
                </button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Quick Search Filters */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-brown-dark dark:text-beige-light mb-4">
          Quick Search Ideas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {[
            { icon: HomeIcon, text: "Family homes with garden", color: "bg-green-100 text-green-700" },
            { icon: CurrencyDollarIcon, text: "Budget-friendly properties", color: "bg-blue-100 text-blue-700" },
            { icon: MapPinIcon, text: "Properties near amenities", color: "bg-purple-100 text-purple-700" },
            { icon: AdjustmentsHorizontalIcon, text: "Luxury properties with features", color: "bg-orange-100 text-orange-700" },
          ].map((item, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(item.text)}
              className="flex items-center p-3 bg-white dark:bg-brown-dark border border-gray-200 dark:border-brown rounded-lg hover:shadow-md transition-all"
            >
              <div className={`p-2 rounded-lg ${item.color} mr-3`}>
                <item.icon className="h-4 w-4" />
              </div>
              <span className="text-sm text-brown-dark dark:text-beige-light">{item.text}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
        >
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {/* Search Results */}
      {results.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-brown-dark dark:text-beige-light">
              Search Results
            </h2>
            <span className="text-gray-600 dark:text-gray-400">
              {results.length} properties found
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {results.map((property) => (
              <motion.div
                key={property.property_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <PropertyCard
                  property={{
                    id: property.property_id,
                    title: property.title,
                    location: property.location,
                    price: property.price,
                    property_type: property.property_type,
                    sale_type: property.sale_type,
                    similarity: property.similarity,
                  }}
                  showSimilarity={true}
                />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {!isSearching && query && results.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="max-w-md mx-auto">
            <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-brown-dark dark:text-beige-light mb-2">
              No properties found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search criteria or use different keywords.
            </p>
            <button
              onClick={() => setQuery('')}
              className="bg-taupe text-white px-6 py-2 rounded-lg hover:bg-brown transition-colors"
            >
              Clear Search
            </button>
          </div>
        </motion.div>
      )}

      {/* Loading State */}
      {isSearching && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="max-w-md mx-auto">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="inline-block"
            >
              <SparklesIcon className="h-16 w-16 text-taupe mx-auto mb-4" />
            </motion.div>
            <h3 className="text-xl font-semibold text-brown-dark dark:text-beige-light mb-2">
              AI is searching...
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Finding properties that match your criteria
            </p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default AIPropertySearch;

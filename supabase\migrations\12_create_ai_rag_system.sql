-- Create AI RAG system for real estate chatbot
-- This migration adds tables and functions for AI-powered property assistance

-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create ai_documents table for storing property-related documents
CREATE TABLE IF NOT EXISTS ai_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('property_listing', 'market_report', 'legal_document', 'brochure', 'inspection_report', 'neighborhood_guide')),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  file_url TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create ai_embeddings table for vector storage
CREATE TABLE IF NOT EXISTS ai_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES ai_documents(id) ON DELETE CASCADE,
  content_chunk TEXT NOT NULL,
  embedding vector(1536), -- OpenAI embedding dimension
  chunk_index INTEGER NOT NULL DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create ai_chat_sessions table for tracking AI conversations
CREATE TABLE IF NOT EXISTS ai_chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_type TEXT DEFAULT 'general' CHECK (session_type IN ('general', 'property_search', 'investment_advice', 'market_analysis', 'support')),
  context JSONB DEFAULT '{}', -- Store conversation context, user preferences, etc.
  property_context UUID[] DEFAULT '{}', -- Array of property IDs discussed in session
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create ai_chat_messages table for AI conversation history
CREATE TABLE IF NOT EXISTS ai_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES ai_chat_sessions(id) ON DELETE CASCADE,
  message_type TEXT NOT NULL CHECK (message_type IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}', -- Store AI model used, tokens, confidence, etc.
  property_references UUID[] DEFAULT '{}', -- Properties mentioned in this message
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create property_embeddings table for semantic property search
CREATE TABLE IF NOT EXISTS property_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  description_embedding vector(1536),
  features_embedding vector(1536),
  location_embedding vector(1536),
  combined_embedding vector(1536),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(property_id)
);

-- Create ai_user_preferences table for personalized recommendations
CREATE TABLE IF NOT EXISTS ai_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  preferences JSONB NOT NULL DEFAULT '{}', -- Budget, location, property type, features, etc.
  search_history JSONB DEFAULT '{}',
  interaction_patterns JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_documents_property_id ON ai_documents(property_id);
CREATE INDEX IF NOT EXISTS idx_ai_documents_type ON ai_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_ai_embeddings_document_id ON ai_embeddings(document_id);
CREATE INDEX IF NOT EXISTS idx_ai_embeddings_vector ON ai_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_ai_chat_sessions_user_id ON ai_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_chat_sessions_status ON ai_chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_ai_chat_messages_session_id ON ai_chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_property_embeddings_property_id ON property_embeddings(property_id);
CREATE INDEX IF NOT EXISTS idx_property_embeddings_combined ON property_embeddings USING ivfflat (combined_embedding vector_cosine_ops) WITH (lists = 100);

-- Enable RLS on all AI tables
ALTER TABLE ai_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_documents
CREATE POLICY "Admins can manage all AI documents" ON ai_documents
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Users can view AI documents" ON ai_documents
  FOR SELECT TO authenticated
  USING (true);

-- RLS Policies for ai_embeddings
CREATE POLICY "Admins can manage all AI embeddings" ON ai_embeddings
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'is_admin' = 'true');

CREATE POLICY "Users can view AI embeddings" ON ai_embeddings
  FOR SELECT TO authenticated
  USING (true);

-- RLS Policies for ai_chat_sessions
CREATE POLICY "Users can manage their own AI chat sessions" ON ai_chat_sessions
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for ai_chat_messages
CREATE POLICY "Users can manage their own AI chat messages" ON ai_chat_messages
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM ai_chat_sessions 
      WHERE ai_chat_sessions.id = ai_chat_messages.session_id 
      AND ai_chat_sessions.user_id = auth.uid()
    )
  );

-- RLS Policies for property_embeddings
CREATE POLICY "Everyone can view property embeddings" ON property_embeddings
  FOR SELECT TO authenticated, anon
  USING (true);

CREATE POLICY "Admins can manage property embeddings" ON property_embeddings
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'is_admin' = 'true');

-- RLS Policies for ai_user_preferences
CREATE POLICY "Users can manage their own AI preferences" ON ai_user_preferences
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Function to search similar properties using embeddings
CREATE OR REPLACE FUNCTION search_similar_properties(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.8,
  match_count int DEFAULT 10
)
RETURNS TABLE (
  property_id UUID,
  similarity float,
  title TEXT,
  location TEXT,
  price NUMERIC,
  property_type TEXT,
  sale_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pe.property_id,
    1 - (pe.combined_embedding <=> query_embedding) as similarity,
    p.title,
    p.location,
    p.price,
    pt.name as property_type,
    st.name as sale_type
  FROM property_embeddings pe
  JOIN properties p ON pe.property_id = p.id
  JOIN property_types pt ON p.property_type_id = pt.id
  JOIN sale_types st ON p.sale_type_id = st.id
  WHERE 1 - (pe.combined_embedding <=> query_embedding) > match_threshold
  ORDER BY pe.combined_embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Function to search relevant documents for RAG
CREATE OR REPLACE FUNCTION search_relevant_documents(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.7,
  match_count int DEFAULT 5,
  document_types text[] DEFAULT NULL
)
RETURNS TABLE (
  document_id UUID,
  content_chunk TEXT,
  similarity float,
  document_title TEXT,
  document_type TEXT,
  property_id UUID
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ae.document_id,
    ae.content_chunk,
    1 - (ae.embedding <=> query_embedding) as similarity,
    ad.title as document_title,
    ad.document_type,
    ad.property_id
  FROM ai_embeddings ae
  JOIN ai_documents ad ON ae.document_id = ad.id
  WHERE 
    1 - (ae.embedding <=> query_embedding) > match_threshold
    AND (document_types IS NULL OR ad.document_type = ANY(document_types))
  ORDER BY ae.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Function to update property embeddings
CREATE OR REPLACE FUNCTION update_property_embedding(
  p_property_id UUID,
  p_description_embedding vector(1536) DEFAULT NULL,
  p_features_embedding vector(1536) DEFAULT NULL,
  p_location_embedding vector(1536) DEFAULT NULL,
  p_combined_embedding vector(1536) DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO property_embeddings (
    property_id, 
    description_embedding, 
    features_embedding, 
    location_embedding, 
    combined_embedding,
    updated_at
  )
  VALUES (
    p_property_id,
    p_description_embedding,
    p_features_embedding,
    p_location_embedding,
    p_combined_embedding,
    now()
  )
  ON CONFLICT (property_id) 
  DO UPDATE SET
    description_embedding = COALESCE(EXCLUDED.description_embedding, property_embeddings.description_embedding),
    features_embedding = COALESCE(EXCLUDED.features_embedding, property_embeddings.features_embedding),
    location_embedding = COALESCE(EXCLUDED.location_embedding, property_embeddings.location_embedding),
    combined_embedding = COALESCE(EXCLUDED.combined_embedding, property_embeddings.combined_embedding),
    updated_at = now();
END;
$$;

-- Function to create or update AI chat session
CREATE OR REPLACE FUNCTION upsert_ai_chat_session(
  p_user_id UUID,
  p_session_type TEXT DEFAULT 'general',
  p_context JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  session_id UUID;
BEGIN
  -- Try to find an active session of the same type
  SELECT id INTO session_id
  FROM ai_chat_sessions
  WHERE user_id = p_user_id 
    AND session_type = p_session_type 
    AND status = 'active'
  ORDER BY updated_at DESC
  LIMIT 1;

  -- If no active session found, create a new one
  IF session_id IS NULL THEN
    INSERT INTO ai_chat_sessions (user_id, session_type, context)
    VALUES (p_user_id, p_session_type, p_context)
    RETURNING id INTO session_id;
  ELSE
    -- Update existing session
    UPDATE ai_chat_sessions 
    SET context = context || p_context, updated_at = now()
    WHERE id = session_id;
  END IF;

  RETURN session_id;
END;
$$;

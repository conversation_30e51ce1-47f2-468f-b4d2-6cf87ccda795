import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  SparklesIcon,
  DocumentTextIcon,
  CpuChipIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { embeddingService } from '../../../lib/embeddingService';
import { aiService } from '../../../lib/aiService';

const AIManagement = () => {
  const [stats, setStats] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const embeddingStats = await embeddingService.getEmbeddingStats();
      setStats(embeddingStats);
    } catch (error) {
      console.error('Error loading stats:', error);
      setError('Failed to load AI statistics');
    }
  };

  const handleProcessAllProperties = async () => {
    setIsProcessing(true);
    setError(null);
    setSuccess(null);
    setProcessingStatus('Starting property processing...');

    try {
      await embeddingService.processAllProperties(3); // Process 3 at a time
      setSuccess('Successfully processed all properties for AI search');
      await loadStats();
    } catch (error) {
      console.error('Error processing properties:', error);
      setError('Failed to process properties: ' + error.message);
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  };

  const handleTestAIResponse = async () => {
    setIsProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      // Create a test session
      const testUserId = 'test-user-id';
      const sessionId = await aiService.createOrGetSession(testUserId, 'general');
      
      // Test AI response
      const response = await aiService.generateResponse(
        'Show me properties under ₦50 million in Lagos',
        sessionId,
        { test: true }
      );

      setSuccess(`AI Test Response: ${response.substring(0, 100)}...`);
    } catch (error) {
      console.error('Error testing AI:', error);
      setError('AI test failed: ' + error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-brown-dark dark:text-beige-light">
            AI Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage AI features, embeddings, and chatbot capabilities
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <SparklesIcon className="h-8 w-8 text-taupe" />
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center"
        >
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-3" />
          <span className="text-red-700">{error}</span>
        </motion.div>
      )}

      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center"
        >
          <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
          <span className="text-green-700">{success}</span>
        </motion.div>
      )}

      {processingStatus && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center"
        >
          <ArrowPathIcon className="h-5 w-5 text-blue-500 mr-3 animate-spin" />
          <span className="text-blue-700">{processingStatus}</span>
        </motion.div>
      )}

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Properties with Embeddings
                </p>
                <p className="text-2xl font-bold text-brown-dark dark:text-beige-light">
                  {stats.propertiesWithEmbeddings}
                </p>
                <p className="text-xs text-gray-500">
                  {stats.embeddingCoverage}% coverage
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <CpuChipIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Properties
                </p>
                <p className="text-2xl font-bold text-brown-dark dark:text-beige-light">
                  {stats.totalProperties}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <DocumentTextIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  AI Documents
                </p>
                <p className="text-2xl font-bold text-brown-dark dark:text-beige-light">
                  {stats.totalDocuments}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Vector Embeddings
                </p>
                <p className="text-2xl font-bold text-brown-dark dark:text-beige-light">
                  {stats.totalEmbeddings}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Property Processing */}
        <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
          <h3 className="text-lg font-semibold text-brown-dark dark:text-beige-light mb-4">
            Property Embeddings
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Process all properties to create AI embeddings for intelligent search and recommendations.
          </p>
          <div className="space-y-3">
            <button
              onClick={handleProcessAllProperties}
              disabled={isProcessing}
              className="w-full bg-taupe text-white px-4 py-2 rounded-lg hover:bg-brown transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isProcessing ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CpuChipIcon className="h-4 w-4 mr-2" />
                  Process All Properties
                </>
              )}
            </button>
            <p className="text-xs text-gray-500">
              This will create vector embeddings for all properties to enable AI-powered search.
            </p>
          </div>
        </div>

        {/* AI Testing */}
        <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
          <h3 className="text-lg font-semibold text-brown-dark dark:text-beige-light mb-4">
            AI System Testing
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Test the AI chatbot functionality and response generation.
          </p>
          <div className="space-y-3">
            <button
              onClick={handleTestAIResponse}
              disabled={isProcessing}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isProcessing ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <SparklesIcon className="h-4 w-4 mr-2" />
                  Test AI Response
                </>
              )}
            </button>
            <p className="text-xs text-gray-500">
              Send a test query to the AI system to verify functionality.
            </p>
          </div>
        </div>
      </div>

      {/* Configuration Info */}
      <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
        <h3 className="text-lg font-semibold text-brown-dark dark:text-beige-light mb-4">
          AI Configuration
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-brown-dark dark:text-beige-light mb-2">
              Features Enabled
            </h4>
            <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>✅ Property Search with AI</li>
              <li>✅ Intelligent Recommendations</li>
              <li>✅ Market Analysis</li>
              <li>✅ Investment Advice</li>
              <li>✅ Real-time Chat Support</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-brown-dark dark:text-beige-light mb-2">
              System Status
            </h4>
            <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>🔗 OpenAI API: Connected</li>
              <li>🗄️ Vector Database: Active</li>
              <li>🤖 Chatbot: Operational</li>
              <li>📊 Analytics: Tracking</li>
              <li>🔒 Security: Enabled</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIManagement;

import { supabase } from './supabase';
import { aiService } from './aiService';

/**
 * Service for managing property embeddings and document processing
 */
export const embeddingService = {
  /**
   * Process and embed a single property
   * @param {Object} property - Property object with all details
   * @returns {Promise<void>}
   */
  async processProperty(property) {
    try {
      // Build comprehensive property description for embedding
      const propertyDescription = this.buildPropertyDescription(property);
      const featuresText = this.buildFeaturesText(property.features || []);
      const locationText = this.buildLocationText(property);
      const combinedText = `${propertyDescription} ${featuresText} ${locationText}`;

      // Generate embeddings for different aspects
      const [
        descriptionEmbedding,
        featuresEmbedding,
        locationEmbedding,
        combinedEmbedding
      ] = await Promise.all([
        aiService.generateEmbedding(propertyDescription),
        aiService.generateEmbedding(featuresText),
        aiService.generateEmbedding(locationText),
        aiService.generateEmbedding(combinedText)
      ]);

      // Store embeddings in database
      const { error } = await supabase.rpc('update_property_embedding', {
        p_property_id: property.id,
        p_description_embedding: descriptionEmbedding,
        p_features_embedding: featuresEmbedding,
        p_location_embedding: locationEmbedding,
        p_combined_embedding: combinedEmbedding
      });

      if (error) throw error;

      console.log(`Successfully processed embeddings for property: ${property.title}`);
    } catch (error) {
      console.error(`Error processing property ${property.id}:`, error);
      throw error;
    }
  },

  /**
   * Process all properties in the database
   * @param {number} batchSize - Number of properties to process at once
   * @returns {Promise<void>}
   */
  async processAllProperties(batchSize = 5) {
    try {
      console.log('Starting to process all properties for embeddings...');

      // Get all properties with their related data
      const { data: properties, error } = await supabase
        .from('properties')
        .select(`
          *,
          property_types(name),
          sale_types(name),
          property_features(
            features(name)
          )
        `);

      if (error) throw error;

      console.log(`Found ${properties.length} properties to process`);

      // Process properties in batches to avoid rate limits
      for (let i = 0; i < properties.length; i += batchSize) {
        const batch = properties.slice(i, i + batchSize);
        
        console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(properties.length / batchSize)}`);
        
        await Promise.all(
          batch.map(property => this.processProperty(property))
        );

        // Add delay between batches to respect rate limits
        if (i + batchSize < properties.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('Successfully processed all properties');
    } catch (error) {
      console.error('Error processing all properties:', error);
      throw error;
    }
  },

  /**
   * Build comprehensive property description for embedding
   * @param {Object} property - Property object
   * @returns {string} Property description
   */
  buildPropertyDescription(property) {
    const propertyType = property.property_types?.name || 'property';
    const saleType = property.sale_types?.name || 'sale';
    
    return `${property.title} is a ${propertyType} for ${saleType} located in ${property.location}. 
    This property features ${property.bedrooms} bedrooms and ${property.bathrooms} bathrooms 
    with ${property.square_feet} square feet of living space. 
    Priced at ₦${property.price.toLocaleString()}. 
    ${property.description || ''} 
    ${property.neighborhood ? `Located in the ${property.neighborhood} neighborhood.` : ''}`;
  },

  /**
   * Build features text for embedding
   * @param {Array} features - Array of property features
   * @returns {string} Features description
   */
  buildFeaturesText(features) {
    if (!features || features.length === 0) {
      return 'No specific features listed.';
    }

    const featureNames = features
      .map(f => f.features?.name || f.name)
      .filter(Boolean)
      .join(', ');

    return `Property features include: ${featureNames}. These amenities enhance the living experience and property value.`;
  },

  /**
   * Build location text for embedding
   * @param {Object} property - Property object
   * @returns {string} Location description
   */
  buildLocationText(property) {
    let locationText = `Located in ${property.location}`;
    
    if (property.neighborhood) {
      locationText += `, specifically in the ${property.neighborhood} area`;
    }

    if (property.latitude && property.longitude) {
      locationText += `. Geographic coordinates: ${property.latitude}, ${property.longitude}`;
    }

    locationText += '. This location offers convenient access to local amenities and transportation.';
    
    return locationText;
  },

  /**
   * Process and store a document with embeddings
   * @param {Object} document - Document object
   * @returns {Promise<void>}
   */
  async processDocument(document) {
    try {
      // Store document in ai_documents table
      const { data: docData, error: docError } = await supabase
        .from('ai_documents')
        .insert({
          title: document.title,
          content: document.content,
          document_type: document.type,
          property_id: document.propertyId,
          file_url: document.fileUrl,
          metadata: document.metadata || {}
        })
        .select()
        .single();

      if (docError) throw docError;

      // Split content into chunks for embedding
      const chunks = this.splitTextIntoChunks(document.content, 1000, 200);

      // Process chunks in batches
      const chunkPromises = chunks.map(async (chunk, index) => {
        const embedding = await aiService.generateEmbedding(chunk);
        
        return supabase.from('ai_embeddings').insert({
          document_id: docData.id,
          content_chunk: chunk,
          embedding: embedding,
          chunk_index: index,
          metadata: { chunk_length: chunk.length }
        });
      });

      await Promise.all(chunkPromises);

      console.log(`Successfully processed document: ${document.title}`);
    } catch (error) {
      console.error(`Error processing document ${document.title}:`, error);
      throw error;
    }
  },

  /**
   * Split text into overlapping chunks for better context preservation
   * @param {string} text - Text to split
   * @param {number} chunkSize - Size of each chunk
   * @param {number} overlap - Overlap between chunks
   * @returns {Array<string>} Array of text chunks
   */
  splitTextIntoChunks(text, chunkSize = 1000, overlap = 200) {
    const chunks = [];
    let start = 0;

    while (start < text.length) {
      let end = start + chunkSize;
      
      // Try to break at sentence boundaries
      if (end < text.length) {
        const lastPeriod = text.lastIndexOf('.', end);
        const lastNewline = text.lastIndexOf('\n', end);
        const breakPoint = Math.max(lastPeriod, lastNewline);
        
        if (breakPoint > start + chunkSize * 0.5) {
          end = breakPoint + 1;
        }
      }

      chunks.push(text.slice(start, end).trim());
      start = end - overlap;
    }

    return chunks.filter(chunk => chunk.length > 50); // Filter out very short chunks
  },

  /**
   * Update embeddings for a specific property
   * @param {string} propertyId - Property ID to update
   * @returns {Promise<void>}
   */
  async updatePropertyEmbedding(propertyId) {
    try {
      // Get property with all related data
      const { data: property, error } = await supabase
        .from('properties')
        .select(`
          *,
          property_types(name),
          sale_types(name),
          property_features(
            features(name)
          )
        `)
        .eq('id', propertyId)
        .single();

      if (error) throw error;

      await this.processProperty(property);
    } catch (error) {
      console.error(`Error updating property embedding ${propertyId}:`, error);
      throw error;
    }
  },

  /**
   * Delete embeddings for a property
   * @param {string} propertyId - Property ID
   * @returns {Promise<void>}
   */
  async deletePropertyEmbedding(propertyId) {
    try {
      const { error } = await supabase
        .from('property_embeddings')
        .delete()
        .eq('property_id', propertyId);

      if (error) throw error;
    } catch (error) {
      console.error(`Error deleting property embedding ${propertyId}:`, error);
      throw error;
    }
  },

  /**
   * Get embedding statistics
   * @returns {Promise<Object>} Embedding statistics
   */
  async getEmbeddingStats() {
    try {
      const [
        { count: propertiesWithEmbeddings },
        { count: totalProperties },
        { count: totalDocuments },
        { count: totalEmbeddings }
      ] = await Promise.all([
        supabase.from('property_embeddings').select('*', { count: 'exact', head: true }),
        supabase.from('properties').select('*', { count: 'exact', head: true }),
        supabase.from('ai_documents').select('*', { count: 'exact', head: true }),
        supabase.from('ai_embeddings').select('*', { count: 'exact', head: true })
      ]);

      return {
        propertiesWithEmbeddings,
        totalProperties,
        totalDocuments,
        totalEmbeddings,
        embeddingCoverage: totalProperties > 0 ? (propertiesWithEmbeddings / totalProperties * 100).toFixed(1) : 0
      };
    } catch (error) {
      console.error('Error getting embedding stats:', error);
      throw error;
    }
  }
};

export default embeddingService;
